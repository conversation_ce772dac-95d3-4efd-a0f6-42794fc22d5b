//! 系统相册访问演示程序
//!
//! 这是一个命令行程序，演示如何使用 system-photo-access 库的各种功能。
//! 可以通过 `cargo run` 命令运行。

use std::env;
use std::process;

fn main() {
    // 解析命令行参数
    let args: Vec<String> = env::args().collect();

    if args.len() > 1 {
        match args[1].as_str() {
            "help" | "--help" | "-h" => {
                print_help();
                return;
            }
            "version" | "--version" | "-v" => {
                print_version();
                return;
            }
            _ => {
                eprintln!("未知命令: {}", args[1]);
                print_help();
                process::exit(1);
            }
        }
    }

    // 默认显示帮助信息
    print_help();
}

/// 打印帮助信息
fn print_help() {
    println!("系统相册访问演示程序");
    println!();
    println!("这是一个演示 swift-bridge 与 iOS/macOS 系统相册集成的项目。");
    println!();
    println!("用法:");
    println!("  cargo run                    显示此帮助信息");
    println!("  cargo run help               显示此帮助信息");
    println!("  cargo run version            显示版本信息");
    println!();
    println!("注意:");
    println!("  此项目需要在 macOS 或 iOS 环境中运行才能访问系统相册。");
    println!("  当前版本主要用于演示 swift-bridge 的集成能力。");
}

/// 打印版本信息
fn print_version() {
    println!("系统相册访问库 v0.1.0");
    println!("基于 swift-bridge 构建");
}
